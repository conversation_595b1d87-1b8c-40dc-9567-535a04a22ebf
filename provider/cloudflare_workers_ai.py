import logging
from dify_plugin import <PERSON><PERSON>rovider
from dify_plugin.entities.model import ModelType
from dify_plugin.errors.model import CredentialsValidateFailedError

logger = logging.getLogger(__name__)


class CloudflareWorkersAIProvider(ModelProvider):
    def validate_provider_credentials(self, credentials: dict) -> None:
        """
        Validate provider credentials

        If validation fails, raise CredentialsValidateFailedError

        :param credentials: provider credentials, credentials form defined in `provider_credential_schema`.
        """
        try:
            # Validate using LLM model instance
            model_instance = self.get_model_instance(ModelType.LLM)
            model_instance.validate_credentials(model="@cf/qwen/qwq-32b-preview", credentials=credentials)
        except CredentialsValidateFailedError as ex:
            raise ex
        except Exception as ex:
            logger.exception(f"{self.get_provider_schema().provider} credentials validate failed")
            raise CredentialsValidateFailedError(str(ex))
