# AIDiBiz Cloudflare Workers AI Plugin for Dify

This is a custom plugin for <PERSON><PERSON> to connect with AI models on Cloudflare Workers AI platform through the Cloudflare API.

## Features

- ✅ Support for Large Language Models (LLM) - @cf/qwen/qwq-32b-preview
- ✅ Support for text embedding models
- ✅ Direct integration with Cloudflare Workers AI
- ✅ Support for streaming and non-streaming responses
- ✅ Configurable model parameters through UI
- ✅ Function calling support (when enabled)
- ✅ Vision support (when enabled)
- ✅ Multi-language support (English and Vietnamese)

## Configuration

### Environment Variables (.env)

```env
API_TOKEN="your-cloudflare-api-token-here"
ACCOUNT_ID="your-cloudflare-account-id"
CONTEXT_SIZE="32768"
MAX_TOKENS="4096"
```

### Configuration Parameters in Dify

When installing the plugin, you need to provide:

1. **API Token**: Cloudflare Workers AI API Token (get from [Cloudflare Dashboard](https://dash.cloudflare.com/profile/api-tokens))
2. **Account ID**: Your Cloudflare Account ID
3. **Model Name**: Model name (e.g., @cf/qwen/qwq-32b-preview)
4. **Context Size**: Context window size (e.g., 32768)
5. **Max Tokens**: Maximum tokens per request (e.g., 4096)
6. **Vision Support**: Enable vision capabilities (Yes/No)
7. **Function Call Support**: Enable function calling (Yes/No)

### Supported Models

1. **LLM Models**:
   - @cf/qwen/qwq-32b-preview (default)
   - @cf/meta/llama-3.1-8b-instruct
   - @cf/microsoft/phi-2
   - @cf/mistral/mistral-7b-instruct-v0.1

2. **Embedding Models**:
   - @cf/baai/bge-base-en-v1.5
   - @cf/baai/bge-small-en-v1.5
   - @cf/baai/bge-large-en-v1.5

## Installation

### 1. Package the plugin

```bash
chmod +x package_plugin.sh
./package_plugin.sh
```

The script will automatically:
- Check operating system
- Download Dify CLI tools
- Create signing key pair
- Package the plugin
- Sign the plugin
- Verify signature

### 2. Install into Dify

1. Configure Dify server to accept signatures:
   ```bash
   mkdir -p docker/volumes/plugin_daemon/public_keys
   cp aidbiz_key_pair.public.pem docker/volumes/plugin_daemon/public_keys
   ```

2. Configure environment variables in `docker-compose.override.yaml`:
   ```yaml
   services:
     plugin_daemon:
       environment:
         FORCE_VERIFYING_SIGNATURE: true
         THIRD_PARTY_SIGNATURE_VERIFICATION_ENABLED: true
         THIRD_PARTY_SIGNATURE_VERIFICATION_PUBLIC_KEYS: /app/storage/public_keys/aidbiz_key_pair.public.pem
   ```

3. Restart Dify services:
   ```bash
   cd docker
   docker compose down
   docker compose up -d
   ```

4. Upload the `aidibiz_cloudflare_workers_ai.signed.difypkg` file in Dify admin interface

## Sử dụng

### Trong Python (test_client.py)

```python
from openai import OpenAI
from dotenv import load_dotenv
import os

load_dotenv()

client = OpenAI(
    api_key=os.getenv("API_KEY"),
    base_url=os.getenv("BASE_URL")
)

# Test LLM
response = client.chat.completions.create(
    model=os.getenv("LLM_MODEL"),
    messages=[
        {"role": "system", "content": "You are a helpful Vietnamese assistant."},
        {"role": "user", "content": "Bạn có thể giúp tôi mô tả về hệ mặt trời không?"}
    ]
)

# Test Embedding
embedding_response = client.embeddings.create(
    model=os.getenv("EMBEDDING_MODEL"),
    input="Xin chào, tôi là một mô hình ngôn ngữ lớn."
)

# Test Vision (with base64 encoded image)
vision_response = client.chat.completions.create(
    model=os.getenv("VISION_MODEL"),
    messages=[
        {
            "role": "user",
            "content": [
                {
                    "type": "image_url",
                    "image_url": {
                        "url": f"data:image/jpeg;base64,{base64_image}"
                    }
                },
                {
                    "type": "text",
                    "text": "Bạn có thể mô tả hình ảnh này không?"
                }
            ]
        }
    ]
)
```

### Trong Dify

1. Đi đến phần "Model Providers"
2. Tìm "FPT Cloud AI" trong danh sách
3. Cấu hình API Key từ FPT Cloud AI Marketplace
4. Chọn mô hình từ danh sách có sẵn:
   - **LLM**: QwQ-32B, DeepSeek-R1, Llama-3.3-70B-Instruct, v.v.
   - **Embedding**: Vietnamese_Embedding, FPT.AI-e5-large, v.v.
   - **Vision**: gemma-3-27b-it, Qwen2.5-VL-7B-Instruct
5. Hoặc nhập tên model tùy chỉnh nếu cần

## Cấu trúc thư mục

```
.
├── manifest.yaml                 # Manifest của plugin
├── main.py                      # Entry point của plugin
├── provider/
│   ├── fpt-cloud.yaml           # Cấu hình provider
│   └── fpt_cloud.py             # Provider implementation
├── models/
│   ├── llm/
│   │   └── llm.py               # LLM model implementation
│   ├── text_embedding/
│   │   └── text_embedding.py    # Text embedding implementation
│   └── multimodal_llm/
│       └── multimodal_llm.py    # Vision Language Model implementation
├── _assets/
│   ├── icon_s_en.svg            # Icon nhỏ
│   └── icon_l_en.svg            # Icon lớn
├── test_client.py               # Script test
├── package_plugin.sh            # Script đóng gói
├── requirements.txt             # Dependencies
├── .env                         # Biến môi trường
├── .difyignore                  # Files to ignore
├── aidibiz-fpt-cloud.difypkg    # Plugin package
├── aidibiz-fpt-cloud.signed.difypkg # Plugin đã ký
├── aidbiz_key_pair.private.pem  # Private key
├── aidbiz_key_pair.public.pem   # Public key
└── README.md                    # Tài liệu này
```

## Phát triển

### Yêu cầu

- Python 3.12+
- Dify Plugin SDK
- OpenAI Python SDK
- Dify Server với plugin support

### Test

```bash
python3 test_client.py
```

## Hỗ trợ

Nếu gặp vấn đề, vui lòng:

1. Kiểm tra API key từ FPT Cloud AI Marketplace
2. Đảm bảo model name chính xác (QwQ-32B, Vietnamese_Embedding, gemma-3-27b-it)
3. Xem logs của Dify
4. Tham khảo tài liệu FPT Cloud AI Marketplace: https://marketplace.fptcloud.com/
5. Liên hệ team AIDiBiz để được hỗ trợ

## Phiên bản

- v1.0.0: Phiên bản đầu tiên với hỗ trợ LLM, text embedding và Vision Language Model
- Tích hợp với FPT Cloud AI Marketplace
- Hỗ trợ các model: QwQ-32B, Vietnamese_Embedding, gemma-3-27b-it
